
**5.1	In Scope**

The testing scope includes the Reltio China MDM total Process (MDM, VOD, CRM, DUC, eDC etc.), the User Requirements, and End to End Integration. The testing is organized into the following categories based on testing layers and complexity:

**Integration Testing**

**API Integration Testing**

1. CRM to MDM (HCP T1 DCR change requests, HCP T2 data changes)
2. MDM to DUC (HCP delta changes, HCO delta changes, Product delta changes, Reference delta changes, DCR responses)
3. DUC to MDM (HCP delta data, Non-HCP data)
4. External Tool to MDM (Bulk upload/update for HCO, Bulk upload/update for Product via ETL)
5. MDM to VOD (HCO DCR requests, HCP change DCR requests via China SFTP)
6. VOD to MDM (HCP full profiles, HCP DCR change responses, HCO DCR responses and full profiles via SFTP)
7. MDM to eDC (HCP incremental data - daily, HCO incremental data - bi-hourly, Product data - daily, Reference data - daily)
8. MDM to eDC (Affiliation data: HCP-HCO daily, HCO-HCO bi-hourly, SKU-Indication daily, SKU-ProductFamily daily)
9. MDM to eDC (Merge Activity incremental data - daily)
10. MDM to CRM (HCP data via SQS queue)
11. eDC to CRM (Data distribution)
12. eDC to DUC (Data distribution)
13. eDC to Downstream Systems (Data distribution)
   

**Testing Key Actions:**

* API Parameter Check Test: Test when parameters are empty, wrong, or out of range.  
* API Response Check Test: Check if the API status codes are correct.  
* API Edge and Error Test: Test API under heavy traffic or many users at the same time.  
* API Network and Server Test: Test API when network is down, slow, or server is not working.  
* API Dependency Test: Test how API works when calling other services like databases or third-party APIs.


**SFTP Integration Testing**

1. MDM to VOD (HCO DCR requests, HCP change DCR requests) - Team: Sylvia
2. VOD to MDM (HCP creation response, HCP full profiles, HCO DCR response and full profiles) - Team: Sylvia
3. MDM to CRM (DCR Response and full profile) - Team: Vic
   

**Testing Key Actions:**

* Fault recovery test: Validate error recovery mechanisms  
* SFTP file transfer test: Conduct end-to-end workflow tests across both systems.  
* Performance and Load Test: Verify multiple file downloads scenarios, the file transmission time  
* Data Consistent test: Validate data Consistent (e.g., Enum values, data volume, field name)  
* Data Scenario test: When the SFTP file is an incremental table, verify data scenarios such as add, modify, and delete.


**Event Integration Testing - Priority: Medium**

1. MDM to eDC (Real-time data synchronization events via Talend - 3 hours frequency)
2. eDC to Downstream Systems (Data distribution events)
3. eDC to CRM (Data distribution events)
4. eDC to DUC (Data distribution events)
5. CRM to MDM (DCR workflow events)
6. VOD to MDM (Validation response events)
7. DUC Event Processing (OMP, SES, A1 events - Event codes: 030, 033, 037, 038, 040, 042)
   

**Testing Key Actions:**

* Event Trigger Testing: Validate that events are properly triggered by system actions.  
* Event Processing Testing: Ensure events are processed correctly and in sequence.  
* Event Recovery Testing: Test event replay and recovery mechanisms.  
* Event Performance Testing: Validate event processing under load conditions.


**Match & Merge Algorithm Testing - Priority: High**

1. Automatic matching rules validation for HCP records  
2. Automatic matching rules validation for HCO records  
3. Manual merge operations testing  
4. Survivorship rules validation  
5. Duplicate detection accuracy testing  
   

**Testing Key Actions:**

* Algorithm Accuracy Testing: Validate matching algorithms identify correct duplicates.  
* False Positive Testing: Ensure algorithms don't incorrectly match different entities.  
* Performance Testing: Test matching performance with large datasets.  
* Rule Configuration Testing: Validate custom matching rules work correctly.  
* Merge Quality Testing: Ensure merged records maintain data quality.

**End to End Testing list - Priority: High**

| \# | Scenario Name | Integration Path | Check point |
| :---- | :---- | :---- | :---- |
| 1 | HCP T1 DCR Change Process | CRM--\>MDM--\>VOD--\>MDM--\>CRM--\>eDC--\>Downstream | 1\) DCR request received correctly 2\) VOD validation completed 3\) Response sent back to CRM 4\) Data updated in all systems 5\) Data distributed via eDC |
| 2 | HCP T2 Data Change Process | CRM--\>MDM--\>eDC--\>Downstream | 1\) Data changes received and processed 2\) Data quality validation passed 3\) Changes reflected in MDM 4\) Data distributed via eDC |
| 3 | HCP Creation Process (Including Appeal) | QR Code/Downstream--\>DUC--\>SMS/E-Sign--\>VOD--\>MDM--\>CRM--\>eDC--\>Downstream | 1\) HCP creation request processed 2\) VOD validation completed 3\) HCP profile created in MDM 4\) Response sent to DUC 5\) Data distributed via eDC |
| 4 | HCO Single Creation Process | MDM--\>VOD--\>MDM--\>eDC--\>CRM/DUC/Downstream | 1\) HCO creation request processed 2\) VOD validation completed 3\) HCO profile created 4\) Data distributed to downstream |
| 5 | HCO Bulk Creation Process | External Tool--\>MDM--\>VOD--\>MDM--\>eDC--\>CRM/DUC/Downstream | 1\) Bulk upload processed successfully 2\) Data validation completed 3\) HCO profiles created in batch 4\) Data distributed via eDC |
| 6 | Ad-hoc HCO Creation Process | DUC--\>MDM--\>VOD--\>eDC--\>CRM/DUC/Downstream | 1\) HCO creation request from DUC processed 2\) VOD validation completed 3\) HCO profile created 4\) Data distributed via eDC |
| 7 | HCO Update Process | MDM--\>VOD--\>MDM--\>eDC--\>CRM/DUC/Downstream | 1\) HCO change requests processed 2\) Data validation completed 3\) Changes reflected in system 4\) Data distributed via eDC |
| 8 | Non-HCP Process | DUC--\>MDM--\>eDC--\>Downstream | 1\) Non-HCP data processed in MDM 2\) Data quality validation passed 3\) Data distributed via eDC |
| 9 | Product Data Management Process | MDM--\>eDC--\>CRM/Downstream | 1\) Product data changes processed 2\) Data synchronized to eDC 3\) Data distributed to downstream systems |
| 10 | Reference Data Management Process | MDM--\>eDC--\>DUC/Downstream | 1\) Reference data changes processed 2\) Data synchronized to eDC 3\) Downstream systems updated |
| 11 | HCP Merge Process | MDM--\>eDC--\>CRM/DUC/Downstream | 1\) HCP merge operation completed 2\) Merge activity recorded 3\) Data distributed via eDC |
| 12 | HCO Merge Process | MDM--\>eDC--\>CRM/DUC/Downstream | 1\) HCO merge operation completed 2\) Merge activity recorded 3\) Data distributed via eDC |

**Functional Testing**

| No | Business Process | Description |
| :---- | :---- | :---- |
| 1 | Data Steward HCP Management | Data Steward can create, update, search, and manage HCP profiles with proper validation |
| 2 | Data Steward HCO Management | Data Steward can create, update, search, and manage HCO profiles with proper validation |
| 3 | Non-HCP Data Management | Data Steward can manage Non-HCP profiles including creation, updates, and consent management |
| 4 | Product Data Management | Data Steward can manage product master data including SKU, Indication, ProductFamily with proper relationships |
| 5 | Reference Data Management | Data Steward can manage reference data including lookups, codes, and master lists with start/end date controls |
| 6 | Match & Merge Operations | System can automatically and manually match and merge duplicate records based on configured rules |
| 7 | Data Quality Validation | System validates data quality according to business rules and flags issues appropriately |
| 8 | Survivorship Rules | System applies survivorship rules correctly when merging records from multiple sources |
| 9 | MDM ID Generation | System generates unique MDM IDs following the specified format (A-202511XX) |
| 10 | Role-based Access Control | Users can access only the functions and data appropriate to their assigned roles |
| 11 | Bulk Upload/Update Operations | Data Steward can perform bulk operations via CSV upload with proper validation and error handling |
| 12 | Data Export Functions | Users can export data in required formats with proper security controls |
| 13 | SFTP Connection Management | System can establish and maintain SFTP connections for data exchange with VOD (Team: Sylvia) |
| 14 | API Endpoint Management | System can manage API endpoints for integration with CRM, DUC, eDC and other systems |
| 15 | Audit Trail Management | System maintains comprehensive audit trails for all data modifications and user actions |
| 16 | HCO-HCO Relationship Management | System can manage complex HCO relationships including Hospital Alliances and Main-Branch structures |
| 17 | eDC Data Outbound Management | System can manage data outbound to eDC with proper frequency controls (HCP daily, HCO bi-hourly, Product daily, Reference daily) |
| 18 | Merge Activity Tracking | System can track and export merge activities with proper timestamp controls |
| 19 | Affiliation Relationship Management | System can manage HCP-HCO, HCO-HCO, SKU-Indication, and SKU-ProductFamily relationships |
| 20 | Event Processing Management | System can process and distribute events to downstream systems (DUC events: 030, 033, 037, 038, 040, 042) |

**Testing Key Actions:**

* Break down test cases by role-specific permissions and workflows.
* Validate edge cases (e.g., error handling, input validation).
* Document test scenarios aligned with use cases in requirement documents.
* Test data quality validation rules and survivorship logic.
* Validate match and merge algorithms with various data scenarios.
* Test DCR (Data Change Request) workflows end-to-end.
* Verify data synchronization across all integrated systems including eDC distribution.
* Test regulatory compliance features (ICP license display, audit trails).
* Validate performance requirements (API latency, SFTP transfer speed, login time).
* Test data governance and security controls.
* Validate HCO-HCO relationships (医联体 Hospital Alliance, 总分院 Main-Branch Hospital).
* Test HCO and HCP alias management and display.
* Validate automatic merge for same VID records.
* Test manual merge operations for potential matches.
* Validate data quality flagging and rejection mechanisms.
* Test VOD validation frequency and scheduling.
* Validate MDM ID generation following CRM format (A-202511XX).
* Test segregation of duties across different data steward roles.
* Validate Chinese regulatory compliance (Cybersecurity Law, DSL, PIPL).
* Test cross-border data flow restrictions and controls.
* Test Non-HCP data processing and consent management workflows.
* Validate eDC data outbound scenarios with proper frequency controls (HCP daily, HCO bi-hourly, Product daily, Reference daily).
* Test Product data management including SKU, Indication, ProductFamily entities and their relationships.
* Validate Merge Activity tracking and export functionality with timestamp controls.
* Test Event processing workflows for DUC events (030, 033, 037, 038, 040, 042).
* Validate Affiliation relationship management (HCP-HCO, HCO-HCO, SKU-Indication, SKU-ProductFamily).
* Test Reference Data Management with start/end date controls and lookup entity management.
* Validate team-specific interface responsibilities (Sylvia: SFTP/VOD, Vic: CRM, Xixi: eDC/Talend).
* Test incremental vs full data export scenarios based on entity types.
* Validate API parameter filtering for entity types and time ranges.

**5.2 Out of Scope**  
The following testing activities are explicitly excluded from this test plan scope:

* **Unit Testing**  
  * **Rationale:** Unit testing is the responsibility of the development team (Reltio) and should be completed before system delivery to the test environment.  
  * **Responsible Party:** Reltio Development Team  
  * **Impact:** Unit test results will be reviewed as part of entrance criteria for SAT testing.  
* **Security Testing**  
  * **Rationale:** Comprehensive security testing requires specialized tools and expertise that are handled by dedicated security teams.  
  * **Responsible Party:** Roche Information Security Team and Reltio Security Team  
  * **Impact:** Security test reports will be reviewed and security requirements validated through functional testing scenarios.  
* **Performance Testing**  
  * **Rationale:** Dedicated performance testing will be conducted separately with specialized tools and environments.  
  * **Responsible Party:** Reltio Performance Testing Team and Roche Infrastructure Team  
  * **Impact:** Performance requirements will be validated through integration testing scenarios, but detailed load testing is excluded.  
* **Infrastructure Testing**  
  * **Rationale:** Infrastructure setup and configuration testing is handled by the deployment teams.  
  * **Responsible Party:** Reltio Deployment Team and Roche Infrastructure Team  
  * **Impact:** Infrastructure readiness is a prerequisite for test execution.  
* **Third-party System Testing**  
  * **Rationale:** Testing of VOD, CRM, and other third-party systems' internal functionality is outside MDM testing scope.  
  * **Responsible Party:** Respective system owners (VOD team, CRM team, etc.)  
  * **Impact:** Integration points with these systems will be tested, but not their internal functionality.
