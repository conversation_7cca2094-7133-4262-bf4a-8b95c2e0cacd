
**5.1	In Scope**

The testing scope includes the Reltio China MDM total Process (MDM, VOD, CRM, DUC, eDC etc.), the User Requirements, and End to End Integration. The testing is organized into the following categories based on testing layers and complexity:

**Integration Testing**

**API Integration Testing**

1. CRM to MDM (HCP T1 DCR change requests, HCP T2 data changes)
2. MDM to DUC (HCP delta changes, HCO delta changes, Product delta changes, Reference delta changes, DCR responses)
3. DUC to MDM (HCP delta data, Non-HCP data, Write list import-HCP exemption)
4. External Tool to MDM (Bulk upload/update for HCO, Bulk upload/update for Product via ETL)
5. MDM to PingID (SSO authentication) - Team: Sylvia
6. VVPM to CRM (Hourly API) - Team: CRM
7. HCP360 to CRM (API) - Team: CRM
8. CRM WeChat Notification Service (API) - Team: CRM
9. MDM Outbound API (Internal APIs for data export to eDC via Talend):
   - Entities API (HCP, HCO, SKU, Indication, ProductFamily data export)
   - Relation API (HCP-HCO, HCO-HCO, SKU-Indication, SKU-ProductFamily relationships export)
   - RDM API (Reference data export)
   - Merge Tree API (Merge activity export)
   

**Testing Key Actions:**

* API Parameter Check Test: Test when parameters are empty, wrong, or out of range.  
* API Response Check Test: Check if the API status codes are correct.  
* API Edge and Error Test: Test API under heavy traffic or many users at the same time.  
* API Network and Server Test: Test API when network is down, slow, or server is not working.  
* API Dependency Test: Test how API works when calling other services like databases or third-party APIs.


**SQS Integration Testing**

1. MDM to CRM (HCP data via SQS queue) - Team: Vic

**SFTP Integration Testing**

1. MDM to VOD (HCP DCR Change requests) - Team: Sylvia
2. MDM to VOD (HCO DCR requests) - Team: Sylvia
3. VOD to MDM (HCP creation response, HCP full profiles, HCO DCR response and full profiles) - Team: Sylvia
4. MDM to CRM (DCR Response and full profile) - Team: Vic
5. CDMS to CRM (Dealer data - Daily) - Team: CRM

**Talend Integration Testing**

1. MDM to eDC (HCP incremental data - daily frequency) - Team: Xixi
2. MDM to eDC (HCO incremental data - bi-hourly frequency) - Team: Xixi
3. MDM to eDC (SKU full data - daily frequency) - Team: Xixi
4. MDM to eDC (Indication full data - daily frequency) - Team: Xixi
5. MDM to eDC (ProductFamily full data - daily frequency) - Team: Xixi
6. MDM to eDC (Reference data full - daily frequency) - Team: Xixi
7. MDM to eDC (HCP-HCO Affiliation incremental - daily frequency) - Team: Xixi
8. MDM to eDC (HCO-HCO Affiliation incremental - bi-hourly frequency) - Team: Xixi
9. MDM to eDC (SKU-Indication Affiliation full - daily frequency) - Team: Xixi
10. MDM to eDC (SKU-ProductFamily Affiliation full - daily frequency) - Team: Xixi
11. MDM to eDC (Merge Activity incremental - daily frequency) - Team: Xixi
   

**Testing Key Actions for SQS Integration:**

* Message Queue Test: Validate SQS message delivery and processing
* Message Format Test: Verify message structure and data integrity
* Error Handling Test: Test message retry and dead letter queue scenarios
* Performance Test: Validate message throughput and latency

**Testing Key Actions for SFTP Integration:**

* Fault recovery test: Validate error recovery mechanisms
* SFTP file transfer test: Conduct end-to-end workflow tests across both systems
* Performance and Load Test: Verify multiple file downloads scenarios, the file transmission time
* Data Consistent test: Validate data consistency (e.g., Enum values, data volume, field name)
* Data Scenario test: When the SFTP file is an incremental table, verify data scenarios such as add, modify, and delete

**Testing Key Actions for Talend Integration:**

* ETL Process Test: Validate Talend job execution and data transformation
* Data Mapping Test: Verify field mapping and data conversion accuracy
* Frequency Test: Validate different frequency scheduling (daily for HCP/Products, bi-hourly for HCO)
* Incremental vs Full Test: Validate incremental exports for HCP/HCO/Relations vs full exports for Products/Reference
* Error Handling Test: Test Talend job failure and recovery mechanisms
* Performance Test: Validate data processing speed and resource utilization
* API Parameter Test: Validate timestamp filtering and entity type filtering for outbound APIs

**Digital Portal Integration Testing**

1. Digital Portal Onboarding Process
2. Digital Portal Offboarding Process
3. Digital Portal Binding Process
4. Digital Portal Notification Process

**CRM Extended Integration Testing**

1. CRM to TOM Integration (User/Position/Territory/Position-Product data)
2. eDC to CRM Integration (Data distribution)
3. CRM to eDC Integration (Data feedback)
4. HLS to eDC Integration


**Event Integration Testing - Priority: Medium**

1. CRM to MDM (DCR workflow events)
2. VOD to MDM (Validation response events via SFTP)
3. DUC Event Processing (OMP, SES, A1 events - Event codes: 030, 033, 037, 038, 040, 042)
4. eDC to Downstream Systems (Data distribution events)
5. eDC to CRM (Data distribution events)
6. eDC to DUC (Data distribution events)
   

**Testing Key Actions:**

* Event Trigger Testing: Validate that events are properly triggered by system actions.  
* Event Processing Testing: Ensure events are processed correctly and in sequence.  
* Event Recovery Testing: Test event replay and recovery mechanisms.  
* Event Performance Testing: Validate event processing under load conditions.


**Match & Merge Algorithm Testing - Priority: High**

1. Automatic matching rules validation for HCP records  
2. Automatic matching rules validation for HCO records  
3. Manual merge operations testing  
4. Survivorship rules validation  
5. Duplicate detection accuracy testing  
   

**Testing Key Actions:**

* Algorithm Accuracy Testing: Validate matching algorithms identify correct duplicates.  
* False Positive Testing: Ensure algorithms don't incorrectly match different entities.  
* Performance Testing: Test matching performance with large datasets.  
* Rule Configuration Testing: Validate custom matching rules work correctly.  
* Merge Quality Testing: Ensure merged records maintain data quality.

**End to End Testing list - Priority: High**

| \# | Scenario Name | Integration Path | Check point |
| :---- | :---- | :---- | :---- |
| 1 | HCP Creation (Including Appeal Process) | QR CODE/Downstream--\>DUC--\>SMS Service--\>DUC--\>E-Sign--\>DUC--\>VOD--\>MDM--\>CRM--\>MDM--\>eDC--\>DUC/Downstream--\>VOD--\>DUC | 1\) QR code scan initiated 2\) SMS/E-Sign verification completed 3\) VOD validation completed 4\) HCP profile created in MDM 5\) Data distributed via eDC 6\) Appeal process handled if needed |
| 2 | HCP Update (Including Appeal Process) | CRM--\>MDM--\>VOD--\>MDM--\>eDC--\>CRM/DUC/Downstream--\>DUC--\>Downstream | 1\) HCP update request from CRM 2\) VOD validation completed 3\) Changes reflected in MDM 4\) Data distributed via eDC 5\) Appeal process handled if needed |
| 3 | HCP Update: T2 attributes | CRM--\>MDM--\>eDC--\>Downstream--\>eDC--\>DUC | 1\) T2 attribute changes received from CRM 2\) Data quality validation passed 3\) Changes reflected in MDM 4\) Data distributed via eDC |
| 4 | Single HCO Creation | MDM--\>VOD--\>MDM--\>eDC--\>CRM/DUC/Downstream | 1\) HCO creation request processed 2\) VOD validation completed 3\) HCO profile created 4\) Data distributed to downstream |
| 5 | Single HCO Update | MDM--\>VOD--\>MDM--\>eDC--\>CRM/DUC/Downstream | 1\) HCO update request processed 2\) VOD validation completed 3\) Changes reflected in system 4\) Data distributed via eDC |
| 6 | Bulk HCO Creation | MDM--\>VOD--\>MDM--\>eDC--\>CRM/DUC/Downstream | 1\) Bulk upload processed successfully 2\) VOD validation completed 3\) HCO profiles created in batch 4\) Data distributed via eDC |
| 7 | Bulk HCO Update | MDM--\>VOD--\>MDM--\>eDC--\>CRM/DUC/Downstream--\>DUC--\>Downstream | 1\) Bulk update processed successfully 2\) VOD validation completed 3\) Changes reflected in system 4\) Data distributed via eDC |
| 8 | Ad-hoc HCO Creation | DUC--\>MDM--\>VOD--\>MDM--\>eDC--\>CRM/DUC/Downstream | 1\) HCO creation request from DUC processed 2\) VOD validation completed 3\) HCO profile created 4\) Data distributed via eDC |
| 9 | Non-HCP Process | DUC--\>MDM | 1\) Non-HCP data processed in MDM 2\) Consent management handled 3\) Data quality validation passed |
| 10 | Product Data Management | MDM--\>eDC--\>CRM | 1\) Product data changes processed 2\) Data synchronized to eDC 3\) Data distributed to CRM |
| 11 | External Tool Integration | External Tool--\>MDM | 1\) Bulk upload/update for HCO processed 2\) Bulk upload/update for Product processed 3\) Data validation completed |
| 12 | Reference Data Management | MDM--\>eDC--\>DUC | 1\) Reference data changes processed 2\) Data synchronized to eDC 3\) DUC systems updated |
| 13 | HCP Merge Process | MDM--\>eDC--\>CRM/DUC/Downstream | 1\) HCP merge operation completed 2\) Merge activity recorded 3\) Data distributed via eDC |
| 14 | HCO Merge Process | MDM--\>eDC--\>CRM/DUC/Downstream | 1\) HCO merge operation completed 2\) Merge activity recorded 3\) Data distributed via eDC |
| 15 | eDC & CRM Integration | eDC--\>CRM--\>eDC--\>HLS--\>eDC | 1\) Data distribution from eDC to CRM 2\) CRM feedback to eDC 3\) HLS integration with eDC |
| 16 | Digital Portal Process | Digital Portal Onboarding/Offboarding/Binding/Notification | 1\) User onboarding completed 2\) Offboarding process handled 3\) Binding operations successful 4\) Notifications delivered |
| 17 | VVPM Integration | VVPM--\>CRM (API/Hourly) | 1\) VVPM data received by CRM 2\) Hourly synchronization completed 3\) Data validation passed |
| 18 | CRM-TOM Integration | TOM--\>CRM (User/Position/Territory/Position-Product/In-Mar Product) | 1\) Employee data synchronized 2\) Position and territory data updated 3\) Product assignments completed |
| 19 | HCP360 Integration | HCP360--\>CRM | 1\) HCP360 data received by CRM 2\) Data integration completed 3\) CRM profiles updated |
| 20 | WeChat Notification Integration | CRM--\>WeChat Notification Service | 1\) Notification trigger from CRM 2\) WeChat group notification sent 3\) Delivery confirmation received |
| 21 | CDMS Integration | CDMS--\>CRM (SFTP/Daily) | 1\) Dealer data received from CDMS 2\) Daily SFTP transfer completed 3\) CRM data updated |
| 22 | Write List Import-HCP Exemption | DUC(O&M)--\>MDM--\>eDC--\>Downstream | 1\) HCP exemption list imported 2\) MDM processing completed 3\) Data distributed via eDC |

**Functional Testing**

| No | Business Process | Description |
| :---- | :---- | :---- |
| 1 | Data Steward HCP Management | Data Steward can create, update, search, and manage HCP profiles with proper validation |
| 2 | Data Steward HCO Management | Data Steward can create, update, search, and manage HCO profiles with proper validation |
| 3 | Non-HCP Data Management | Data Steward can manage Non-HCP profiles including creation, updates, and consent management |
| 4 | Product Data Management | Data Steward can manage product master data including SKU, Indication, ProductFamily with proper relationships |
| 5 | Reference Data Management | Data Steward can manage reference data including lookups, codes, and master lists with start/end date controls |
| 6 | Match & Merge Operations | System can automatically and manually match and merge duplicate records based on configured rules |
| 7 | Data Quality Validation | System validates data quality according to business rules and flags issues appropriately |
| 8 | Survivorship Rules | System applies survivorship rules correctly when merging records from multiple sources |
| 9 | MDM ID Generation | System generates unique MDM IDs following the specified format (A-202511XX) |
| 10 | Role-based Access Control | Users can access only the functions and data appropriate to their assigned roles |
| 11 | Bulk Upload/Update Operations | Data Steward can perform bulk operations via CSV upload with proper validation and error handling |
| 12 | Data Export Functions | Users can export data in required formats with proper security controls |
| 13 | SFTP Connection Management | System can establish and maintain SFTP connections for data exchange with VOD (Team: Sylvia) |
| 14 | API Endpoint Management | System can manage API endpoints for integration with CRM, DUC, eDC and other systems |
| 15 | Audit Trail Management | System maintains comprehensive audit trails for all data modifications and user actions |
| 16 | HCO-HCO Relationship Management | System can manage complex HCO relationships including Hospital Alliances and Main-Branch structures |
| 17 | eDC Data Outbound Management | System can manage data outbound to eDC with proper frequency controls (HCP daily, HCO bi-hourly, Product daily, Reference daily) |
| 18 | Merge Activity Tracking | System can track and export merge activities with proper timestamp controls |
| 19 | Affiliation Relationship Management | System can manage HCP-HCO, HCO-HCO, SKU-Indication, and SKU-ProductFamily relationships |
| 20 | Event Processing Management | System can process and distribute events to downstream systems (DUC events: 030, 033, 037, 038, 040, 042) |
| 21 | Digital Portal Management | System can handle digital portal onboarding, offboarding, binding, and notification processes |
| 22 | VVPM Integration Management | System can handle VVPM data integration with CRM on hourly basis |
| 23 | TOM Integration Management | System can manage TOM integration for user, position, territory, and product data |
| 24 | HCP360 Integration Management | System can handle HCP360 data integration with CRM |
| 25 | WeChat Notification Management | System can trigger and manage WeChat group notifications to representatives |
| 26 | CDMS Integration Management | System can handle CDMS dealer data integration via daily SFTP |
| 27 | Write List Import Management | System can manage HCP exemption write list imports from DUC O&M |
| 28 | Appeal Process Management | System can handle appeal processes for HCP creation and updates |
| 29 | Incremental vs Full Export Management | System can manage different export types (incremental for HCP/HCO/Relations, full for Products/Reference) |
| 30 | Multi-frequency Data Synchronization | System can handle different synchronization frequencies (daily, bi-hourly, hourly) based on data types |

**Testing Key Actions:**

* Break down test cases by role-specific permissions and workflows.
* Validate edge cases (e.g., error handling, input validation).
* Document test scenarios aligned with use cases in requirement documents.
* Test data quality validation rules and survivorship logic.
* Validate match and merge algorithms with various data scenarios.
* Test DCR (Data Change Request) workflows end-to-end.
* Verify data synchronization across all integrated systems including eDC distribution.
* Test regulatory compliance features (ICP license display, audit trails).
* Validate performance requirements (API latency, SFTP transfer speed, login time).
* Test data governance and security controls.
* Validate HCO-HCO relationships (医联体 Hospital Alliance, 总分院 Main-Branch Hospital).
* Test HCO and HCP alias management and display.
* Validate automatic merge for same VID records.
* Test manual merge operations for potential matches.
* Validate data quality flagging and rejection mechanisms.
* Test VOD validation frequency and scheduling.
* Validate MDM ID generation following CRM format (A-202511XX).
* Test segregation of duties across different data steward roles.
* Validate Chinese regulatory compliance (Cybersecurity Law, DSL, PIPL).
* Test cross-border data flow restrictions and controls.
* Test Non-HCP data processing and consent management workflows.
* Validate eDC data outbound scenarios with accurate frequency controls (HCP daily, HCO bi-hourly, SKU/Indication/ProductFamily daily, Reference daily).
* Test Product data management including SKU, Indication, ProductFamily entities and their relationships (SKU-Indication, SKU-ProductFamily).
* Validate Merge Activity tracking and export functionality with timestamp controls (daily incremental).
* Test Event processing workflows for DUC events (030, 033, 037, 038, 040, 042).
* Validate Affiliation relationship management with correct frequencies (HCP-HCO daily, HCO-HCO bi-hourly, SKU-Indication daily, SKU-ProductFamily daily).
* Test Reference Data Management with start/end date controls and lookup entity management.
* Validate team-specific interface responsibilities (Sylvia: SFTP/VOD/PingID, Vic: CRM/SQS, Xixi: eDC/Talend).
* Test incremental vs full data export scenarios (Incremental: HCP, HCO, Relations, Merge Activity; Full: SKU, Indication, ProductFamily, Reference).
* Validate API parameter filtering for entity types and time ranges with proper timestamp formats.
* Test Digital Portal workflows (Onboarding, Offboarding, Binding, Notification).
* Validate VVPM to CRM hourly API integration.
* Test CRM-TOM integration for user, position, territory, and product data.
* Validate HCP360 to CRM integration.
* Test WeChat notification service integration with CRM.
* Validate CDMS to CRM daily SFTP integration for dealer data.
* Test Write List Import for HCP exemption from DUC O&M.
* Validate Appeal Process workflows for HCP creation and updates.
* Test complex integration paths with multiple system hops (e.g., QR Code→DUC→SMS→E-Sign→VOD→MDM→CRM→eDC→Downstream).
* Validate eDC bidirectional integration with CRM and HLS.
* Test proper handling of different data types with their specific frequencies and export types.
* Validate MDM Outbound API functionality for Entities, Relations, RDM, and Merge Tree APIs.
* Test SQS message queue functionality for MDM to CRM HCP data transfer.
* Validate proper error handling and recovery mechanisms across all integration types.

**5.2 Out of Scope**  
The following testing activities are explicitly excluded from this test plan scope:

* **Unit Testing**  
  * **Rationale:** Unit testing is the responsibility of the development team (Reltio) and should be completed before system delivery to the test environment.  
  * **Responsible Party:** Reltio Development Team  
  * **Impact:** Unit test results will be reviewed as part of entrance criteria for SAT testing.  
* **Security Testing**  
  * **Rationale:** Comprehensive security testing requires specialized tools and expertise that are handled by dedicated security teams.  
  * **Responsible Party:** Roche Information Security Team and Reltio Security Team  
  * **Impact:** Security test reports will be reviewed and security requirements validated through functional testing scenarios.  
* **Performance Testing**  
  * **Rationale:** Dedicated performance testing will be conducted separately with specialized tools and environments.  
  * **Responsible Party:** Reltio Performance Testing Team and Roche Infrastructure Team  
  * **Impact:** Performance requirements will be validated through integration testing scenarios, but detailed load testing is excluded.  
* **Infrastructure Testing**  
  * **Rationale:** Infrastructure setup and configuration testing is handled by the deployment teams.  
  * **Responsible Party:** Reltio Deployment Team and Roche Infrastructure Team  
  * **Impact:** Infrastructure readiness is a prerequisite for test execution.  
* **Third-party System Testing**  
  * **Rationale:** Testing of VOD, CRM, and other third-party systems' internal functionality is outside MDM testing scope.  
  * **Responsible Party:** Respective system owners (VOD team, CRM team, etc.)  
  * **Impact:** Integration points with these systems will be tested, but not their internal functionality.
