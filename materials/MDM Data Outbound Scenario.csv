No	JsonFileType	Full/Incremental	Frequency	API	JsonFile to be shared with EDC	Input Parameters for filtering the entity type and time range	Reltio Primary/Foreign Keys	API URL	Relation JsonFile_	Comments																						
1	HCO	Incremental	bi-hourly	Entities API	2025/8/1	"Parameter name: filter
Parameter value: (range(timestamp,1720077328000,1752557560000) and equals(type, 'configuration/entityTypes/HCO'))

Note: Time range (highlighted in red) to be updated based on the incremental exports taken."	Entity ID	"Reference of the full API given below:

https://cn01-test.reltiocn.cn/jobs/export/J4cWsn9txL2q0xp/entities?filter=(range(timestamp,1720077328000,1752557560000) and equals(sourceSystems,�VEEVA�) and equals(type,'configuration/entityTypes/HCO'))&distributed=true&taskPartsCount=4&options=parallelExecution&fileFormat=json&filenameTemplate=hco-"	"Affiliation (HCP to HCO)
Affiliation (HCO to HCO)"																							
2	HCP	Incremental	daily	Entities API	2025/8/1	"Parameter name: filter
Parameter value: (range(timestamp,1720077328000,1752557560000) and equals(type, 'configuration/entityTypes/HCP'))

Note: Time range (highlighted in red) to be updated based on the incremental exports taken."	HCPMDMID	"Reference of the full API given below:

https://cn01-test.reltiocn.cn/jobs/export/J4cWsn9txL2q0xp/entities?filter=(range(timestamp,1720077328000,1752557560000) and equals(sourceSystems,�VEEVA�) and equals(type,'configuration/entityTypes/HCP'))&distributed=true&taskPartsCount=4&options=parallelExecution&fileFormat=json&filenameTemplate=hcp-"																								
3	Affiliation (HCP to HCO)	Incremental	daily	Affiliation API	2025/8/1	"Parameter name: filter
Parameter value:
(range(timestamp,1720077328000,1752557560000) and equals(type, 'configuration/relationTypes/HCPtoHCO'))

Note: 
1. Time range (highlighted in red) to be updated based on the incremental exports taken.
2. Relation (highlighted in blue) to be updated based on RelationType Name. "	"RelationCrosswalkID=<RelationType>_<StartObject>_<EndObject>

Note:
1. RelationType- Name of the Relation
2. StartObject- Crosswalk ID of the Start Entity Object of the relation
3. EndObject- Crosswalk ID of the End Entity Object of the relation"	"Reference of the full API given below:

https://cn01-test.reltiocn.cn/jobs/export/J4cWsn9txL2q0xp/relations?filter=(range(timestamp,1720077328000,1752557560000) and equals(sourceSystems,�VEEVA�) and equals(type,'configuration/RelationTypes/HCPisAffiliatedwithHCO'))&distributed=true&taskPartsCount=4&options=parallelExecution&fileFormat=json"																								
4	Affiliation (HCO to HCO)	Incremental	bi-hourly	Affiliation API	2025/8/8	"Parameter name: filter
Parameter value:
(range(timestamp,1720077328000,1752557560000) and equals(type, 'configuration/relationTypes/HCOtoHCO'))

Note: 
1. Time range (highlighted in red) to be updated based on the incremental exports taken.
2. Relation (highlighted in blue) to be updated based on RelationType Name. "	"RelationCrosswalkID=<RelationType>_<StartObject>_<EndObject>

Note:
1. RelationType- Name of the Relation
2. StartObject- Crosswalk ID of the Start Entity Object of the relation
3. EndObject- Crosswalk ID of the End Entity Object of the relation"	"Reference of the full API given below:

https://cn01-test.reltiocn.cn/jobs/export/J4cWsn9txL2q0xp/relations?filter=(range(timestamp,1720077328000,1752557560000) and equals(sourceSystems,�VEEVA�) and equals(type,'configuration/RelationTypes/HCOtoHCO'))&distributed=true&taskPartsCount=4&options=parallelExecution&fileFormat=json"		__________																						
5	SKU	Full	daily	Entities API	2025/8/8	"Parameter name: filter
Parameter value: (range(timestamp,1720077328000,1752557560000) and equals(type, 'configuration/entityTypes/SKU'))

Note: Time range (highlighted in red) to be updated based on the incremental exports taken."	"SKUMDMCode
(e.g., SKU00001)"	"Reference of the full API given below:

https://cn01-test.reltiocn.cn/jobs/export/J4cWsn9txL2q0xp/entities?filter=(range(timestamp,1720077328000,1752557560000) and equals(sourceSystems,�VEEVA�) and equals(type,'configuration/entityTypes/SKU'))&distributed=true&taskPartsCount=4&options=parallelExecution&fileFormat=json&filenameTemplate=sku-"																								
6	Indication	Full	daily	Entities API	2025/8/8	"Parameter name: filter
Parameter value:
(range(timestamp,1720077328000,1752557560000) and equals(type, 'configuration/entityTypes/Indication'))

Note: Time range (highlighted in red) to be updated based on the incremental exports taken."	"MDMIndicationCode
(e.g., IND00001)
"	"Reference of the full API given below:

https://cn01-test.reltiocn.cn/jobs/export/J4cWsn9txL2q0xp/entities?filter=(range(timestamp,1720077328000,1752557560000) and equals(sourceSystems,�VEEVA�) and equals(type,'configuration/entityTypes/Indication'))&distributed=true&taskPartsCount=4&options=parallelExecution&fileFormat=json&filenameTemplate=Indication-"																								
7	ProductFamily	Full	daily	Entities API	2025/8/8	"Parameter name: filter
Parameter value: (range(timestamp,1720077328000,1752557560000) and equals(type, 'configuration/entityTypes/ProductFamily'))

Note: Time range (highlighted in red) to be updated based on the incremental exports taken."	"MDMProductFamilyCode
(e.g., PF00001)"	"Reference of the full API given below:

https://cn01-test.reltiocn.cn/jobs/export/J4cWsn9txL2q0xp/entities?filter=(range(timestamp,1720077328000,1752557560000) and equals(sourceSystems,�VEEVA�) and equals(type,'configuration/entityTypes/ProductFamily'))&distributed=true&taskPartsCount=4&options=parallelExecution&fileFormat=json&filenameTemplate=ProductFamily-"																								
8	Affiliation (SKU to Indication)	Full	daily	Affiliation API	2025/8/8	"Parameter name: filter
Parameter value:
(range(timestamp,1720077328000,1752557560000) and equals(type, 'configuration/relationTypes/ProducttoIndication'))

Note: 
1. Time range (highlighted in red) to be updated based on the incremental exports taken.
2. Relation (highlighted in blue) to be updated based on RelationType Name. "	"RelationCrosswalkID=<RelationType>_<StartObject>_<EndObject>

Note:
1. RelationType- Name of the Relation
2. StartObject- Crosswalk ID of the Start Entity Object of the relation
3. EndObject- Crosswalk ID of the End Entity Object of the relation"	"Reference of the full API given below:

https://cn01-test.reltiocn.cn/jobs/export/J4cWsn9txL2q0xp/relations?filter=(range(timestamp,1720077328000,1752557560000) and equals(sourceSystems,�VEEVA�) and equals(type,'configuration/RelationTypes/ProducttoIndication'))&distributed=true&taskPartsCount=4&options=parallelExecution&fileFormat=json"																								
9	Affiliation (SKU to Product Family)	Full	daily	Affiliation API	2025/8/8	"Parameter name: filter
Parameter value: (range(timestamp,1720077328000,1752557560000) and equals(type, 'configuration/relationTypes/ProducttoProductFamily>'))

Note: 
1. Time range (highlighted in red) to be updated based on the incremental exports taken.
2. Relation (highlighted in blue) to be updated based on RelationType Name. "	"RelationCrosswalkID=<RelationType>_<StartObject>_<EndObject>

Note:
1. RelationType- Name of the Relation
2. StartObject- Crosswalk ID of the Start Entity Object of the relation
3. EndObject- Crosswalk ID of the End Entity Object of the relation"	"Reference of the full API given below:

https://cn01-test.reltiocn.cn/jobs/export/J4cWsn9txL2q0xp/relations?filter=(range(timestamp,1720077328000,1752557560000) and equals(sourceSystems,�VEEVA�) and equals(type,'configuration/RelationTypes/ProducttoProductFamily'))&distributed=true&taskPartsCount=4&options=parallelExecution&fileFormat=json"																								
10	Reference Data	Full	daily	RDM	2025/8/8	"Parameter name: filter
Parameter value:
(equals(type,'LookupEntityName') and equals(startDate,'startDate') and (endDate,'endDate'))

Note: 
1. LookupEntityName- Name of the Lookup Entity Type
1. startDate - date when the lookup value will be enabled
2. endDate - date when a lookup value will be disabled "	N/A																									
11	Merge Activity	Incremental	daily	Merge Tree API	2025/8/15	"Body Parameters:
{
""startDate"": 1522228618192,
""endDate"": ""2018-03-28T14:16:58.391+0500""
}

Note: 
1. Time range (highlighted in red) to be updated based on the incremental exports taken.
2. Timestamp can be mentioned in Epoch time (number of milliseconds since 1 Jan 1970) format or Date Format ( YYYY-MM-dd'T'HH:mm:ss.SSSZ or 
MM-dd-YYYY HH.mm)"	N/A																									
12	HCO Merge	Incremental	bi-hourly	Entities API	2025/8/15					_______ins_code																						